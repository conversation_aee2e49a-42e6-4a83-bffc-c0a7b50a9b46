<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Maroon Color Palette Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="public/css/main.css" rel="stylesheet">
    <style>
        .color-demo {
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            color: white;
            font-weight: bold;
        }
        .primary-maroon { background-color: var(--primary-maroon); }
        .secondary-maroon { background-color: var(--secondary-maroon); }
        .dark-maroon { background-color: var(--dark-maroon); }
        .light-maroon { background-color: var(--light-maroon); color: var(--dark-maroon); }
        .accent-maroon { background-color: var(--accent-maroon); }
        .highlight { background-color: var(--highlight); }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1>Maroon Color Palette Test</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h2>Color Variables</h2>
                <div class="color-demo primary-maroon">Primary Maroon: #5a0017</div>
                <div class="color-demo secondary-maroon">Secondary Maroon: #3d000f</div>
                <div class="color-demo dark-maroon">Dark Maroon: #2a000a</div>
                <div class="color-demo light-maroon">Light Maroon: #f5e6ea</div>
                <div class="color-demo accent-maroon">Accent Maroon: #7a1c2a</div>
                <div class="color-demo highlight">Highlight: #9e3a4a</div>
            </div>
            
            <div class="col-md-6">
                <h2>UI Elements Test</h2>
                
                <!-- Button Test -->
                <div class="mb-3">
                    <button class="btn btn-primary" style="background-color: var(--accent-color); border-color: var(--accent-color);">Primary Button</button>
                    <button class="btn btn-outline-primary" style="color: var(--accent-color); border-color: var(--accent-color);">Outline Button</button>
                </div>
                
                <!-- Card Test -->
                <div class="card mb-3" style="background-color: var(--surface-color);">
                    <div class="card-header" style="background-color: var(--light-maroon); color: var(--heading-color);">
                        Card Header
                    </div>
                    <div class="card-body">
                        <h5 class="card-title" style="color: var(--heading-color);">Card Title</h5>
                        <p class="card-text" style="color: var(--default-color);">This is a test card to demonstrate the maroon color palette in action.</p>
                        <a href="#" class="btn" style="background-color: var(--accent-color); color: var(--contrast-color);">Action Button</a>
                    </div>
                </div>
                
                <!-- Navigation Test -->
                <nav class="navbar navbar-expand-lg" style="background-color: var(--surface-color);">
                    <div class="container-fluid">
                        <a class="navbar-brand" href="#" style="color: var(--heading-color);">Brand</a>
                        <div class="navbar-nav">
                            <a class="nav-link" href="#" style="color: var(--nav-color);">Home</a>
                            <a class="nav-link" href="#" style="color: var(--nav-hover-color);">About</a>
                            <a class="nav-link" href="#" style="color: var(--nav-color);">Contact</a>
                        </div>
                    </div>
                </nav>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h2>Typography Test</h2>
                <h1 style="color: var(--heading-color);">Heading 1 - Maroon Theme</h1>
                <h2 style="color: var(--heading-color);">Heading 2 - Maroon Theme</h2>
                <h3 style="color: var(--heading-color);">Heading 3 - Maroon Theme</h3>
                <p style="color: var(--default-color);">This is body text using the default color from our maroon palette. It should be easily readable and provide good contrast against the light background.</p>
                <p><a href="#" style="color: var(--accent-color);">This is a link</a> using the accent color from our maroon palette.</p>
            </div>
        </div>
    </div>
</body>
</html>
